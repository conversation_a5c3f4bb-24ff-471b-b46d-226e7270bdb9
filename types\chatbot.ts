// Chatbot Types and Interfaces

export interface ChatMessage {
  id: string;
  message: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  session_id?: string;
  conversation_id?: string;
  metadata?: {
    intent?: string;
    confidence?: number;
    entities?: Record<string, any>;
    response_type?: string;
    quick_replies?: QuickReply[];
    form_fields?: FormField[];
    redirect_url?: string;
  };
}

export interface QuickReply {
  id: string;
  text: string;
  intent: string;
  payload?: Record<string, any>;
  icon?: string;
}

export interface QuickAction {
  id: string;
  text: string;
  intent: string;
  icon: string;
  description?: string;
  payload?: Record<string, any>;
}

export interface FormField {
  name: string;
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'file';
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
}

export interface ChatbotResponse {
  message: string;
  session_id: string;
  conversation_id: string;
  response_type: 'text' | 'quick_replies' | 'form_guidance' | 'redirect' | 'error';
  metadata?: {
    intent?: string;
    confidence?: number;
    entities?: Record<string, any>;
    quick_replies?: QuickReply[];
    form_fields?: FormField[];
    redirect_url?: string;
    reference_number?: string;
    status?: string;
  };
}

export interface IntentDetectionResponse {
  intent: string;
  confidence: number;
  entities: Record<string, any>;
  session_id: string;
}

export interface ChatbotSession {
  session_id: string;
  conversation_id?: string;
  user_context?: {
    user_id?: string;
    preferences?: Record<string, any>;
  };
  created_at: Date;
  last_activity: Date;
}

export interface ChatbotState {
  messages: ChatMessage[];
  session: ChatbotSession | null;
  isLoading: boolean;
  isTyping: boolean;
  error: string | null;
  quickActions: QuickAction[];
  currentForm: {
    fields: FormField[];
    title?: string;
    description?: string;
  } | null;
}

export interface ChatbotConfig {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  theme?: 'light' | 'dark' | 'auto';
  width?: number;
  height?: number;
  minimized?: boolean;
  showQuickActions?: boolean;
  enableVoiceInput?: boolean;
  enableFileUpload?: boolean;
  confidenceThreshold?: number;
  typingDelay?: number;
  autoGreeting?: boolean;
  greetingMessage?: string;
}

// API Request/Response Types
export interface ChatbotMessageRequest {
  message: string;
  session_id?: string;
  conversation_id?: string;
  user_context?: {
    user_id?: string;
  };
}

export interface IntentDetectionRequest {
  message: string;
  session_id?: string;
  user_context?: {
    user_id?: string;
  };
}

export interface HandleIntentRequest {
  intent: string;
  entities?: Record<string, any>;
  session_id?: string;
  conversation_id?: string;
  user_context?: {
    user_id?: string;
  };
}

// Utility Types
export type ChatbotIntent = 
  | 'greeting'
  | 'register_complaint'
  | 'check_status'
  | 'submit_feedback'
  | 'upload_document'
  | 'generate_content'
  | 'audit_questions'
  | 'help'
  | 'goodbye'
  | 'unknown';

export type MessageType = 'text' | 'quick_reply' | 'form_submission' | 'file_upload';

export interface ChatbotError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Event Types for Chatbot Actions
export interface ChatbotEvent {
  type: 'message_sent' | 'intent_detected' | 'form_submitted' | 'quick_action_clicked' | 'session_started' | 'session_ended';
  payload: Record<string, any>;
  timestamp: Date;
}

// Form Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface FormSubmissionData {
  [fieldName: string]: any;
}

// Voice Input Types (for future extension)
export interface VoiceConfig {
  enabled: boolean;
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
}

// Analytics Types (for future extension)
export interface ChatbotAnalytics {
  session_id: string;
  user_id?: string;
  messages_count: number;
  intents_detected: string[];
  forms_completed: number;
  session_duration: number;
  satisfaction_score?: number;
}

// Component Props Types
export interface ChatMessageProps {
  message: ChatMessage;
  isLatest?: boolean;
  onQuickReply?: (reply: QuickReply) => void;
}

export interface QuickActionsProps {
  actions: QuickAction[];
  onActionClick: (action: QuickAction) => void;
  loading?: boolean;
}

export interface DynamicFormProps {
  fields: FormField[];
  title?: string;
  description?: string;
  onSubmit: (data: FormSubmissionData) => void;
  onCancel?: () => void;
  loading?: boolean;
}

export interface TypingIndicatorProps {
  visible: boolean;
  message?: string;
}

// Hook Types
export interface UseChatbotReturn {
  state: ChatbotState;
  actions: {
    sendMessage: (message: string, type?: MessageType) => Promise<void>;
    sendQuickReply: (reply: QuickReply) => Promise<void>;
    submitForm: (data: FormSubmissionData) => Promise<void>;
    detectIntent: (message: string) => Promise<IntentDetectionResponse>;
    handleIntent: (intent: string, entities?: Record<string, any>) => Promise<void>;
    loadQuickActions: () => Promise<void>;
    startSession: (userId?: string) => void;
    endSession: () => void;
    clearMessages: () => void;
    retryLastMessage: () => Promise<void>;
  };
  utils: {
    generateSessionId: () => string;
    generateConversationId: () => string;
    formatMessage: (text: string) => string;
    validateForm: (data: FormSubmissionData, fields: FormField[]) => ValidationResult;
  };
}

// Constants
export const CHATBOT_INTENTS = {
  GREETING: 'greeting',
  REGISTER_COMPLAINT: 'register_complaint',
  CHECK_STATUS: 'check_status',
  SUBMIT_FEEDBACK: 'submit_feedback',
  UPLOAD_DOCUMENT: 'upload_document',
  GENERATE_CONTENT: 'generate_content',
  AUDIT_QUESTIONS: 'audit_questions',
  HELP: 'help',
  GOODBYE: 'goodbye',
  UNKNOWN: 'unknown'
} as const;

export const RESPONSE_TYPES = {
  TEXT: 'text',
  QUICK_REPLIES: 'quick_replies',
  FORM_GUIDANCE: 'form_guidance',
  REDIRECT: 'redirect',
  ERROR: 'error'
} as const;

export const MESSAGE_SENDERS = {
  USER: 'user',
  BOT: 'bot'
} as const;
