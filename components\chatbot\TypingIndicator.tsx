"use client"

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Bot } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TypingIndicatorProps {
  visible: boolean;
  message?: string;
  className?: string;
}

export function TypingIndicator({ 
  visible, 
  message = "Assistant is typing...",
  className 
}: TypingIndicatorProps) {
  if (!visible) return null;

  return (
    <div className={cn("flex justify-start mb-4", className)}>
      <div className="max-w-[80%] flex items-start space-x-2">
        <div className="flex-shrink-0 mt-1">
          <Bot className="h-4 w-4 text-gray-500" />
        </div>
        
        <Card className="bg-gray-50 border-gray-200 shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center space-x-2">
              {/* Animated typing dots */}
              <div className="flex space-x-1">
                <div 
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: '0ms' }}
                />
                <div 
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: '150ms' }}
                />
                <div 
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: '300ms' }}
                />
              </div>
              
              {/* Optional message */}
              {message && (
                <span className="text-sm text-gray-600 ml-2">
                  {message}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default TypingIndicator;
