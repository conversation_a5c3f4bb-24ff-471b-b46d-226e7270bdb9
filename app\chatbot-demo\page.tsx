"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Chatbot } from '@/components/chatbot'
import { ChatbotConfig } from '@/types/chatbot'
import { MessageCircle, Settings, Palette, MapPin, Bot, Zap, FileText, Search, Upload, Brain, CheckSquare } from 'lucide-react'

export default function ChatbotDemo() {
  const [config, setConfig] = useState<ChatbotConfig>({
    position: 'bottom-right',
    theme: 'light',
    width: 400,
    height: 600,
    minimized: false,
    showQuickActions: true,
    enableVoiceInput: false,
    enableFileUpload: true,
    confidenceThreshold: 0.5,
    autoGreeting: true,
    greetingMessage: "Hello! I'm your medical assistant. How can I help you today?"
  })

  const [userId, setUserId] = useState('demo_user_123')
  const [sessionInfo, setSessionInfo] = useState<string | null>(null)

  const handleConfigChange = (key: keyof ChatbotConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const exampleIntents = [
    {
      title: "Register Complaint",
      icon: FileText,
      description: "Submit a new medical complaint",
      examples: [
        "I want to file a complaint about my treatment",
        "Register a complaint about medication side effects",
        "Submit complaint about doctor behavior"
      ]
    },
    {
      title: "Check Status",
      icon: Search,
      description: "Check the status of existing complaints",
      examples: [
        "What's the status of complaint COMP2024001?",
        "Check my complaint status",
        "Is my complaint resolved?"
      ]
    },
    {
      title: "Upload Document",
      icon: Upload,
      description: "Upload medical documents or files",
      examples: [
        "I need to upload a medical report",
        "Upload prescription document",
        "Attach lab results"
      ]
    },
    {
      title: "Generate Content",
      icon: Brain,
      description: "Generate policies, procedures, or content",
      examples: [
        "Generate a patient privacy policy",
        "Create infection control procedure",
        "Draft medication safety guidelines"
      ]
    },
    {
      title: "Audit Questions",
      icon: CheckSquare,
      description: "Generate audit questions for compliance",
      examples: [
        "Generate audit questions for patient safety",
        "Create compliance checklist",
        "Audit questions for medication management"
      ]
    }
  ]

  return (
    <div className="container mx-auto py-8 px-4 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-3">
          <Bot className="h-8 w-8 text-blue-600" />
          Advanced Medical Chatbot Demo
        </h1>
        <p className="text-gray-600">
          Interactive demonstration of the intelligent medical assistant chatbot with full backend integration.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Configuration Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Settings className="h-5 w-5" />
                Chatbot Configuration
              </CardTitle>
              <CardDescription>
                Customize the chatbot behavior and appearance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Position Setting */}
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Position
                </Label>
                <Select 
                  value={config.position} 
                  onValueChange={(value: any) => handleConfigChange('position', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bottom-right">Bottom Right</SelectItem>
                    <SelectItem value="bottom-left">Bottom Left</SelectItem>
                    <SelectItem value="top-right">Top Right</SelectItem>
                    <SelectItem value="top-left">Top Left</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Theme Setting */}
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  Theme
                </Label>
                <Select 
                  value={config.theme} 
                  onValueChange={(value: any) => handleConfigChange('theme', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="auto">Auto</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Dimensions */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Width (px)</Label>
                <Input
                  type="number"
                  value={config.width}
                  onChange={(e) => handleConfigChange('width', parseInt(e.target.value))}
                  min="300"
                  max="600"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Height (px)</Label>
                <Input
                  type="number"
                  value={config.height}
                  onChange={(e) => handleConfigChange('height', parseInt(e.target.value))}
                  min="400"
                  max="800"
                />
              </div>

              {/* Feature Toggles */}
              <div className="space-y-3 pt-2 border-t">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Show Quick Actions</Label>
                  <Switch
                    checked={config.showQuickActions}
                    onCheckedChange={(checked) => handleConfigChange('showQuickActions', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-sm">Auto Greeting</Label>
                  <Switch
                    checked={config.autoGreeting}
                    onCheckedChange={(checked) => handleConfigChange('autoGreeting', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-sm">File Upload</Label>
                  <Switch
                    checked={config.enableFileUpload}
                    onCheckedChange={(checked) => handleConfigChange('enableFileUpload', checked)}
                  />
                </div>
              </div>

              {/* Confidence Threshold */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Confidence Threshold: {config.confidenceThreshold}
                </Label>
                <Input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={config.confidenceThreshold}
                  onChange={(e) => handleConfigChange('confidenceThreshold', parseFloat(e.target.value))}
                />
              </div>

              {/* User ID */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">User ID</Label>
                <Input
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  placeholder="Enter user ID"
                />
              </div>

              {/* Current Settings Display */}
              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-2">Current Settings:</h4>
                <div className="space-y-1">
                  <Badge variant="outline" className="text-xs">
                    {config.position} • {config.theme}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {config.width}×{config.height}px
                  </Badge>
                  {sessionInfo && (
                    <Badge variant="secondary" className="text-xs">
                      Session: {sessionInfo.slice(-8)}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API Status */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Zap className="h-4 w-4" />
                API Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Chatbot Message</span>
                  <Badge variant="secondary">Ready</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Intent Detection</span>
                  <Badge variant="secondary">Ready</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Handle Intent</span>
                  <Badge variant="secondary">Ready</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Quick Actions</span>
                  <Badge variant="secondary">Ready</Badge>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  All endpoints are configured and ready for testing.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Demo Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                How to Use the Chatbot
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Click the blue chat button to open the intelligent assistant</li>
                <li>Use quick action buttons for common tasks or type natural language</li>
                <li>The bot will detect your intent and provide appropriate responses</li>
                <li>Fill out dynamic forms when prompted for detailed information</li>
                <li>Customize settings in the left panel to test different configurations</li>
              </ol>
            </CardContent>
          </Card>

          {/* Example Intents */}
          <Card>
            <CardHeader>
              <CardTitle>Supported Intents & Examples</CardTitle>
              <CardDescription>
                Try these example messages to see how the chatbot handles different intents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {exampleIntents.map((intent, index) => {
                  const Icon = intent.icon;
                  return (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon className="h-5 w-5 text-blue-600" />
                        <h4 className="font-medium">{intent.title}</h4>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{intent.description}</p>
                      <div className="space-y-2">
                        {intent.examples.map((example, exIndex) => (
                          <div key={exIndex} className="text-sm bg-gray-50 p-2 rounded border-l-2 border-blue-200">
                            "{example}"
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          <Card>
            <CardHeader>
              <CardTitle>Advanced Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Intelligence Features</h4>
                  <ul className="text-sm space-y-1 text-gray-600">
                    <li>• Intent detection with confidence scoring</li>
                    <li>• Multi-turn conversation management</li>
                    <li>• Session and conversation tracking</li>
                    <li>• Dynamic form generation</li>
                    <li>• Context-aware responses</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">UI/UX Features</h4>
                  <ul className="text-sm space-y-1 text-gray-600">
                    <li>• Floating responsive chat widget</li>
                    <li>• Quick action buttons</li>
                    <li>• Typing indicators and animations</li>
                    <li>• Error handling with retry</li>
                    <li>• Theme and position customization</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Integration Code */}
          <Card>
            <CardHeader>
              <CardTitle>Integration Code</CardTitle>
              <CardDescription>
                Copy this code to add the chatbot to your application
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                <pre>{`import { Chatbot } from '@/components/chatbot'

function App() {
  return (
    <div>
      {/* Your app content */}
      <Chatbot
        config={{
          position: "${config.position}",
          theme: "${config.theme}",
          width: ${config.width},
          height: ${config.height},
          showQuickActions: ${config.showQuickActions},
          autoGreeting: ${config.autoGreeting},
          confidenceThreshold: ${config.confidenceThreshold}
        }}
        userId="${userId}"
        onSessionStart={(sessionId) => console.log('Session started:', sessionId)}
        onError={(error) => console.error('Chatbot error:', error)}
      />
    </div>
  )
}`}</pre>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Chatbot Component */}
      <Chatbot
        config={config}
        userId={userId}
        onSessionStart={(sessionId) => setSessionInfo(sessionId)}
        onSessionEnd={() => setSessionInfo(null)}
        onError={(error) => console.error('Chatbot error:', error)}
      />
    </div>
  )
}
