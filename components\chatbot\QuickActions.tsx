"use client"

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Search, 
  MessageSquare, 
  Upload, 
  Brain, 
  CheckSquare,
  Plus,
  Loader2,
  Sparkles
} from 'lucide-react';
import { QuickAction } from '@/types/chatbot';
import { cn } from '@/lib/utils';

interface QuickActionsProps {
  actions: QuickAction[];
  onActionClick: (action: QuickAction) => void;
  loading?: boolean;
  className?: string;
  variant?: 'grid' | 'list' | 'compact';
  showDescriptions?: boolean;
}

const iconMap = {
  'register_complaint': FileText,
  'check_status': Search,
  'submit_feedback': MessageSquare,
  'upload_document': Upload,
  'generate_content': Brain,
  'audit_questions': CheckSquare,
  'help': Plus,
  'greeting': <PERSON>rk<PERSON>,
  'default': Plus
};

export function QuickActions({ 
  actions, 
  onActionClick, 
  loading = false,
  className,
  variant = 'grid',
  showDescriptions = true
}: QuickActionsProps) {
  const getIcon = (intent: string) => {
    const IconComponent = iconMap[intent as keyof typeof iconMap] || iconMap.default;
    return IconComponent;
  };

  const getIntentColor = (intent: string) => {
    const colors = {
      'register_complaint': 'text-red-600 bg-red-50 border-red-200 hover:bg-red-100',
      'check_status': 'text-blue-600 bg-blue-50 border-blue-200 hover:bg-blue-100',
      'submit_feedback': 'text-green-600 bg-green-50 border-green-200 hover:bg-green-100',
      'upload_document': 'text-purple-600 bg-purple-50 border-purple-200 hover:bg-purple-100',
      'generate_content': 'text-orange-600 bg-orange-50 border-orange-200 hover:bg-orange-100',
      'audit_questions': 'text-indigo-600 bg-indigo-50 border-indigo-200 hover:bg-indigo-100',
      'help': 'text-gray-600 bg-gray-50 border-gray-200 hover:bg-gray-100',
      'greeting': 'text-blue-600 bg-blue-50 border-blue-200 hover:bg-blue-100'
    };
    return colors[intent as keyof typeof colors] || colors.help;
  };

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm text-gray-500">Loading quick actions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!actions || actions.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-4">
          <div className="text-center text-sm text-gray-500">
            No quick actions available
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderGridView = () => (
    <div className="grid grid-cols-2 gap-3">
      {actions.map((action) => {
        const Icon = getIcon(action.intent);
        const colorClasses = getIntentColor(action.intent);
        
        return (
          <Button
            key={action.id}
            variant="outline"
            className={cn(
              "h-auto p-4 flex flex-col items-center space-y-2 text-center",
              colorClasses
            )}
            onClick={() => onActionClick(action)}
            disabled={loading}
          >
            <Icon className="h-6 w-6" />
            <div className="space-y-1">
              <div className="font-medium text-sm">{action.text}</div>
              {showDescriptions && action.description && (
                <div className="text-xs opacity-75 line-clamp-2">
                  {action.description}
                </div>
              )}
            </div>
          </Button>
        );
      })}
    </div>
  );

  const renderListView = () => (
    <div className="space-y-2">
      {actions.map((action) => {
        const Icon = getIcon(action.intent);
        const colorClasses = getIntentColor(action.intent);
        
        return (
          <Button
            key={action.id}
            variant="outline"
            className={cn(
              "w-full h-auto p-3 flex items-center justify-start space-x-3",
              colorClasses
            )}
            onClick={() => onActionClick(action)}
            disabled={loading}
          >
            <Icon className="h-5 w-5 flex-shrink-0" />
            <div className="flex-1 text-left space-y-1">
              <div className="font-medium text-sm">{action.text}</div>
              {showDescriptions && action.description && (
                <div className="text-xs opacity-75">
                  {action.description}
                </div>
              )}
            </div>
          </Button>
        );
      })}
    </div>
  );

  const renderCompactView = () => (
    <div className="flex flex-wrap gap-2">
      {actions.map((action) => {
        const Icon = getIcon(action.intent);
        
        return (
          <Button
            key={action.id}
            size="sm"
            variant="outline"
            className="flex items-center space-x-2 h-8"
            onClick={() => onActionClick(action)}
            disabled={loading}
          >
            <Icon className="h-3 w-3" />
            <span className="text-xs">{action.text}</span>
          </Button>
        );
      })}
    </div>
  );

  const renderContent = () => {
    switch (variant) {
      case 'list':
        return renderListView();
      case 'compact':
        return renderCompactView();
      default:
        return renderGridView();
    }
  };

  return (
    <Card className={cn("w-full", className)}>
      {variant !== 'compact' && (
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-blue-600" />
            How can I help you today?
          </CardTitle>
        </CardHeader>
      )}
      <CardContent className={variant === 'compact' ? "p-3" : "p-4 pt-0"}>
        {renderContent()}
        
        {/* Additional info */}
        {variant === 'grid' && actions.length > 0 && (
          <div className="mt-4 pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Or type your message below</span>
              <Badge variant="outline" className="text-xs">
                {actions.length} actions
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default QuickActions;
