// Chatbot Components Export Index

export { default as Chatbot } from './Chatbot';
export { default as ChatMessage } from './ChatMessage';
export { default as QuickActions } from './QuickActions';
export { default as DynamicForm } from './DynamicForm';
export { default as TypingIndicator } from './TypingIndicator';

// Re-export types for convenience
export type {
  ChatMessage as ChatMessageType,
  QuickReply,
  QuickAction,
  FormField,
  FormSubmissionData,
  ChatbotResponse,
  IntentDetectionResponse,
  ChatbotSession,
  ChatbotState,
  ChatbotConfig,
  ChatbotIntent,
  MessageType,
  ChatbotError,
  ChatbotEvent,
  ValidationResult,
  VoiceConfig,
  ChatbotAnalytics,
  ChatMessageProps,
  QuickActionsProps,
  DynamicFormProps,
  TypingIndicatorProps,
  UseChatbotReturn
} from '@/types/chatbot';

// Re-export hook
export { useChatbot } from '@/hooks/useChatbot';

// Re-export constants
export {
  CHATBOT_INTENTS,
  RESPONSE_TYPES,
  MESSAGE_SENDERS
} from '@/types/chatbot';
