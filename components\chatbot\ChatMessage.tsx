"use client"

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  User, 
  Bot, 
  Clock, 
  AlertCircle, 
  CheckCircle,
  MessageSquare,
  ArrowRight
} from 'lucide-react';
import { ChatMessage as ChatMessageType, QuickReply } from '@/types/chatbot';
import { cn } from '@/lib/utils';

interface ChatMessageProps {
  message: ChatMessageType;
  isLatest?: boolean;
  onQuickReply?: (reply: QuickReply) => void;
  className?: string;
}

export function ChatMessage({ 
  message, 
  isLatest = false, 
  onQuickReply,
  className 
}: ChatMessageProps) {
  const isUser = message.sender === 'user';
  const isError = message.metadata?.response_type === 'error';
  const hasQuickReplies = message.metadata?.quick_replies && message.metadata.quick_replies.length > 0;

  const formatTimestamp = (date: Date) => {
    return new Intl.DateTimeFormat('en-NZ', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(date);
  };

  const renderMessageContent = () => {
    // Handle different response types
    switch (message.metadata?.response_type) {
      case 'error':
        return (
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm">{message.message}</p>
              {message.metadata?.entities?.error_code && (
                <p className="text-xs text-red-400 mt-1">
                  Error Code: {message.metadata.entities.error_code}
                </p>
              )}
            </div>
          </div>
        );

      case 'form_guidance':
        return (
          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <MessageSquare className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm">{message.message}</p>
            </div>
            {message.metadata?.form_fields && (
              <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-xs text-blue-700 font-medium mb-2">
                  Form fields required:
                </p>
                <div className="flex flex-wrap gap-1">
                  {message.metadata.form_fields.map((field, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 'redirect':
        return (
          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <ArrowRight className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm">{message.message}</p>
            </div>
            {message.metadata?.redirect_url && (
              <Button
                size="sm"
                variant="outline"
                className="mt-2"
                onClick={() => window.open(message.metadata?.redirect_url, '_blank')}
              >
                Open Link
                <ArrowRight className="h-3 w-3 ml-1" />
              </Button>
            )}
          </div>
        );

      default:
        return (
          <div className="flex items-start gap-2">
            {!isUser && (
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
            )}
            <p className="text-sm whitespace-pre-wrap">{message.message}</p>
          </div>
        );
    }
  };

  const renderQuickReplies = () => {
    if (!hasQuickReplies || !onQuickReply) return null;

    return (
      <div className="mt-3 space-y-2">
        <p className="text-xs text-gray-500 font-medium">Quick replies:</p>
        <div className="flex flex-wrap gap-2">
          {message.metadata!.quick_replies!.map((reply) => (
            <Button
              key={reply.id}
              size="sm"
              variant="outline"
              className="text-xs h-7 px-3 hover:bg-blue-50 hover:border-blue-300"
              onClick={() => onQuickReply(reply)}
            >
              {reply.icon && <span className="mr-1">{reply.icon}</span>}
              {reply.text}
            </Button>
          ))}
        </div>
      </div>
    );
  };

  const renderMetadata = () => {
    if (!message.metadata || isUser) return null;

    const { intent, confidence, entities } = message.metadata;

    return (
      <div className="mt-2 space-y-1">
        {intent && (
          <Badge variant="secondary" className="text-xs">
            Intent: {intent}
          </Badge>
        )}
        {confidence !== undefined && confidence < 0.8 && (
          <Badge variant="outline" className="text-xs text-orange-600">
            Low confidence: {Math.round(confidence * 100)}%
          </Badge>
        )}
        {entities?.reference_number && (
          <Badge variant="outline" className="text-xs">
            Ref: {entities.reference_number}
          </Badge>
        )}
        {entities?.status && (
          <Badge 
            variant={entities.status === 'resolved' ? 'default' : 'secondary'}
            className="text-xs"
          >
            {entities.status}
          </Badge>
        )}
      </div>
    );
  };

  return (
    <div className={cn(
      "flex mb-4",
      isUser ? "justify-end" : "justify-start",
      className
    )}>
      <div className={cn(
        "max-w-[80%] space-y-1",
        isUser ? "items-end" : "items-start"
      )}>
        {/* Message bubble */}
        <Card className={cn(
          "shadow-sm border",
          isUser 
            ? "bg-blue-600 text-white border-blue-600" 
            : isError
            ? "bg-red-50 text-red-800 border-red-200"
            : "bg-white text-gray-800 border-gray-200"
        )}>
          <CardContent className="p-3">
            {renderMessageContent()}
          </CardContent>
        </Card>

        {/* Quick replies */}
        {renderQuickReplies()}

        {/* Metadata */}
        {renderMetadata()}

        {/* Timestamp and sender info */}
        <div className={cn(
          "flex items-center gap-2 text-xs text-gray-500",
          isUser ? "justify-end" : "justify-start"
        )}>
          {isUser ? (
            <User className="h-3 w-3" />
          ) : (
            <Bot className="h-3 w-3" />
          )}
          <span>{isUser ? 'You' : 'Assistant'}</span>
          <Clock className="h-3 w-3" />
          <span>{formatTimestamp(message.timestamp)}</span>
        </div>

        {/* Session info for debugging (only show for latest message in dev) */}
        {process.env.NODE_ENV === 'development' && isLatest && (
          <div className="text-xs text-gray-400 space-y-1">
            {message.session_id && (
              <div>Session: {message.session_id.slice(-8)}</div>
            )}
            {message.conversation_id && (
              <div>Conversation: {message.conversation_id.slice(-8)}</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default ChatMessage;
