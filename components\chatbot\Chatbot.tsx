"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  MessageCircle, 
  Send, 
  X, 
  Minimize2, 
  Maximize2, 
  RotateCcw,
  Loader2,
  AlertCircle,
  Settings,
  Trash2
} from 'lucide-react';
import { useChatbot } from '@/hooks/useChatbot';
import { ChatbotConfig, QuickAction } from '@/types/chatbot';
import { cn } from '@/lib/utils';

// Import chatbot components
import ChatMessage from './ChatMessage';
import QuickActions from './QuickActions';
import DynamicForm from './DynamicForm';
import TypingIndicator from './TypingIndicator';

interface ChatbotProps {
  config?: ChatbotConfig;
  userId?: string;
  className?: string;
  onSessionStart?: (sessionId: string) => void;
  onSessionEnd?: () => void;
  onError?: (error: string) => void;
}

export function Chatbot({ 
  config = {},
  userId,
  className,
  onSessionStart,
  onSessionEnd,
  onError
}: ChatbotProps) {
  // Configuration with defaults
  const {
    position = 'bottom-right',
    theme = 'light',
    width = 400,
    height = 600,
    minimized: defaultMinimized = false,
    showQuickActions = true,
    confidenceThreshold = 0.5,
    autoGreeting = true,
    greetingMessage = "Hello! I'm your medical assistant. How can I help you today?"
  } = config;

  // Local state
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(defaultMinimized);
  const [inputValue, setInputValue] = useState('');
  const [showSettings, setShowSettings] = useState(false);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Chatbot hook
  const { state, actions, utils } = useChatbot({
    userId,
    autoGreeting,
    confidenceThreshold
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [state.messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen, isMinimized]);

  // Handle session events
  useEffect(() => {
    if (state.session?.session_id && onSessionStart) {
      onSessionStart(state.session.session_id);
    }
  }, [state.session?.session_id, onSessionStart]);

  useEffect(() => {
    if (state.error && onError) {
      onError(state.error);
    }
  }, [state.error, onError]);

  // Position classes
  const getPositionClasses = () => {
    const positions = {
      'bottom-right': 'bottom-4 right-4',
      'bottom-left': 'bottom-4 left-4',
      'top-right': 'top-4 right-4',
      'top-left': 'top-4 left-4'
    };
    return positions[position];
  };

  // Handle message sending
  const handleSendMessage = async () => {
    if (!inputValue.trim() || state.isLoading) return;

    const message = inputValue.trim();
    setInputValue('');

    try {
      await actions.sendMessage(message);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  // Handle quick action clicks
  const handleQuickAction = async (action: QuickAction) => {
    try {
      await actions.handleIntent(action.intent, action.payload);
    } catch (error) {
      console.error('Failed to handle quick action:', error);
    }
  };

  // Handle quick reply clicks
  const handleQuickReply = async (reply: any) => {
    try {
      await actions.sendQuickReply(reply);
    } catch (error) {
      console.error('Failed to send quick reply:', error);
    }
  };

  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    try {
      await actions.submitForm(data);
    } catch (error) {
      console.error('Failed to submit form:', error);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle session management
  const handleStartNewSession = () => {
    actions.startSession(userId);
  };

  const handleEndSession = () => {
    actions.endSession();
    if (onSessionEnd) {
      onSessionEnd();
    }
  };

  const handleClearMessages = () => {
    actions.clearMessages();
  };

  // Render floating button when closed
  if (!isOpen) {
    return (
      <div className={cn("fixed z-50", getPositionClasses(), className)}>
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full w-14 h-14 bg-blue-600 hover:bg-blue-700 shadow-lg"
          size="icon"
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("fixed z-50", getPositionClasses(), className)}>
      <Card 
        className={cn(
          "shadow-xl border transition-all duration-300",
          theme === 'dark' ? 'bg-gray-900 border-gray-700' : 'bg-white border-gray-200',
          isMinimized ? 'h-auto' : ''
        )}
        style={{ 
          width: width,
          height: isMinimized ? 'auto' : height 
        }}
      >
        {/* Header */}
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <MessageCircle className="h-4 w-4 text-blue-600" />
              Medical Assistant
              {state.session && (
                <span className="text-xs text-gray-500">
                  ({state.session.session_id.slice(-6)})
                </span>
              )}
            </CardTitle>
            
            <div className="flex items-center gap-1">
              {state.error && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={actions.retryLastMessage}
                  title="Retry last message"
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
              )}
              
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setShowSettings(!showSettings)}
                title="Settings"
              >
                <Settings className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setIsMinimized(!isMinimized)}
                title={isMinimized ? "Maximize" : "Minimize"}
              >
                {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setIsOpen(false)}
                title="Close"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Settings Panel */}
          {showSettings && !isMinimized && (
            <div className="mt-2 p-2 bg-gray-50 rounded-lg space-y-2">
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleClearMessages}
                  className="text-xs"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Clear
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleStartNewSession}
                  className="text-xs"
                >
                  <RotateCcw className="h-3 w-3 mr-1" />
                  New Session
                </Button>
              </div>
            </div>
          )}
        </CardHeader>

        {/* Content */}
        {!isMinimized && (
          <CardContent className="p-0 flex flex-col h-full">
            {/* Messages Area */}
            <ScrollArea className="flex-1 px-4">
              <div className="space-y-2">
                {/* Quick Actions (show when no messages or only greeting) */}
                {showQuickActions && state.messages.length <= 1 && state.quickActions.length > 0 && (
                  <div className="mb-4">
                    <QuickActions
                      actions={state.quickActions}
                      onActionClick={handleQuickAction}
                      loading={state.isLoading}
                      variant="grid"
                    />
                  </div>
                )}

                {/* Messages */}
                {state.messages.map((message, index) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    isLatest={index === state.messages.length - 1}
                    onQuickReply={handleQuickReply}
                  />
                ))}

                {/* Typing Indicator */}
                <TypingIndicator visible={state.isTyping} />

                {/* Error Alert */}
                {state.error && (
                  <Alert variant="destructive" className="mx-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-sm">
                      {state.error}
                      <Button
                        variant="link"
                        size="sm"
                        className="p-0 h-auto ml-2 text-red-600"
                        onClick={actions.retryLastMessage}
                      >
                        Retry
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Dynamic Form */}
            {state.currentForm && (
              <div className="p-4 border-t">
                <DynamicForm
                  fields={state.currentForm.fields}
                  title={state.currentForm.title}
                  description={state.currentForm.description}
                  onSubmit={handleFormSubmit}
                  onCancel={() => actions.clearMessages()}
                  loading={state.isLoading}
                />
              </div>
            )}

            {/* Input Area */}
            <div className="border-t p-4">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  disabled={state.isLoading}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={state.isLoading || !inputValue.trim()}
                  size="icon"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {state.isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}

export default Chatbot;
